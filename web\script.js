document.getElementById('startButton').addEventListener('click', startTest);

async function startTest() {
    document.getElementById('pingResult').textContent = 'Testing...';
    document.getElementById('downloadResult').textContent = 'Testing...';
    document.getElementById('uploadResult').textContent = 'Testing...';

    const ping = await runPingTest();
    document.getElementById('pingResult').textContent = ping.toFixed(2);

    const download = await runDownloadTest();
    document.getElementById('downloadResult').textContent = download.toFixed(2);

    const upload = await runUploadTest();
    document.getElementById('uploadResult').textContent = upload.toFixed(2);
}

async function runPingTest() {
    const numPings = 5;
    let totalPing = 0;

    for (let i = 0; i < numPings; i++) {
        const start = performance.now();
        await fetch('/ping');
        const end = performance.now();
        totalPing += (end - start);
    }
    return totalPing / numPings;
}

async function runDownloadTest() {
    const downloadSize = 10 * 1024 * 1024; // 10 MB
    const startTime = performance.now();
    const response = await fetch(`/download?size=${downloadSize}`);
    const reader = response.body.getReader();
    let receivedLength = 0;
    while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        receivedLength += value.length;
    }
    const endTime = performance.now();
    const duration = (endTime - startTime) / 1000; // seconds
    const bitsReceived = receivedLength * 8;
    return (bitsReceived / duration) / (1024 * 1024); // Mbps
}

async function runUploadTest() {
    const uploadSize = 5 * 1024 * 1024; // 5 MB
    const data = new Uint8Array(uploadSize).map(() => Math.floor(Math.random() * 256)); // Random data
    const startTime = performance.now();
    await fetch('/upload', {
        method: 'POST',
        body: data,
        headers: {
            'Content-Type': 'application/octet-stream',
        },
    });
    const endTime = performance.now();
    const duration = (endTime - startTime) / 1000; // seconds
    const bitsUploaded = uploadSize * 8;
    return (bitsUploaded / duration) / (1024 * 1024); // Mbps
}