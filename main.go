package main

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"time"
)

const (
	port = ":8080"
	webDir = "./web"
)

func main() {
	// Serve static files from the "web" directory
	http.Handle("/", http.FileServer(http.Dir(webDir)))

	// Download handler
	http.HandleFunc("/download", func(w http.ResponseWriter, r *http.Request) {
		sizeStr := r.URL.Query().Get("size")
		size, err := strconv.ParseInt(sizeStr, 10, 64)
		if err != nil || size <= 0 {
			size = 100 * 1024 * 1024 // Default to 100MB if size is not specified or invalid
		}

		w.Header().Set("Content-Type", "application/octet-stream")
		w.Header().Set("Content-Length", strconv.FormatInt(size, 10))

		// Write random bytes to the response writer
		buf := make([]byte, 4096)
		for i := int64(0); i < size; i += int64(len(buf)) {
			_, err := w.Write(buf)
			if err != nil {
				log.Printf("Error writing to response: %v", err)
				return
			}
		}
	})

	// Upload handler
	http.HandleFunc("/upload", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Only POST method is allowed", http.StatusMethodNotAllowed)
			return
		}

		// Read the request body to simulate upload
		_, err := io.Copy(io.Discard, r.Body)
		if err != nil {
			http.Error(w, fmt.Sprintf("Error reading request body: %v", err), http.StatusInternalServerError)
			return
		}

		w.WriteHeader(http.StatusOK)
		fmt.Fprint(w, "Upload successful")
	})

	// Ping handler
	http.HandleFunc("/ping", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprint(w, time.Now().UnixNano()/int64(time.Millisecond)) // Return current timestamp in milliseconds
	})

	log.Printf("Server starting on port %s", port)
	log.Printf("Serving static files from %s", webDir)
	log.Fatal(http.ListenAndServe(port, nil))
}
